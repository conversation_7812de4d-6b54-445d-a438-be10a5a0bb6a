<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON>o OAuth <PERSON>gin{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if 'username' in session %}
    <!-- Top Header for all devices -->
    <header class="app-header">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="app-title">
                <a href="{{ url_for('welcome') }}">
                    <i class="fas fa-key me-2"></i>Maximo OAuth
                </a>
            </div>
            <div class="d-flex align-items-center">
                <div class="d-none d-md-flex me-3">
                    <a href="{{ url_for('welcome') }}" class="nav-link me-3">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="{{ url_for('profile') }}" class="nav-link me-3">
                        <i class="fas fa-user me-1"></i>Profile
                    </a>
                    <a href="{{ url_for('sync') }}" class="nav-link me-3">
                        <i class="fas fa-sync-alt me-1"></i>Sync
                    </a>
                    <a href="{{ url_for('enhanced_workorders') }}" class="nav-link me-3">
                        <i class="fas fa-clipboard-list me-1"></i>Work Orders
                    </a>
                </div>
                <span class="user-info d-none d-md-inline-block">
                    <i class="fas fa-user-circle me-1"></i>{{ session['username'] }}
                </span>
            </div>
        </div>
    </header>

    <!-- Bottom Navigation for Mobile -->
    <nav class="mobile-nav d-md-none">
        <a href="{{ url_for('welcome') }}" class="nav-link" data-page="welcome">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="{{ url_for('profile') }}" class="nav-link" data-page="profile">
            <i class="fas fa-user"></i>
            <span>Profile</span>
        </a>
        <a href="{{ url_for('sync') }}" class="nav-link" data-page="sync">
            <i class="fas fa-sync-alt"></i>
            <span>Sync</span>
        </a>
        <a href="{{ url_for('enhanced_workorders') }}" class="nav-link" data-page="workorders">
            <i class="fas fa-clipboard-list"></i>
            <span>Work Orders</span>
            <span class="nav-badge" id="workOrdersBadge" style="display: none;">0</span>
        </a>
        <a href="{{ url_for('logout') }}" class="nav-link" data-page="logout">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>
    </nav>
    {% endif %}

    <main class="app-content">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-0">Developed by Praba Krishna @2023</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <script>
    // Enhanced Mobile Navigation
    document.addEventListener('DOMContentLoaded', function() {
        // Set active navigation state based on current page
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.mobile-nav .nav-link');

        // Remove all active classes first
        navLinks.forEach(link => link.classList.remove('active'));

        // Determine which nav item should be active
        let activeDataPage = '';
        if (currentPath === '/' || currentPath === '/welcome') {
            activeDataPage = 'welcome';
        } else if (currentPath.includes('/profile') || currentPath.includes('/enhanced-profile')) {
            activeDataPage = 'profile';
        } else if (currentPath.includes('/sync')) {
            activeDataPage = 'sync';
        } else if (currentPath.includes('/workorder') || currentPath.includes('/enhanced-workorder')) {
            activeDataPage = 'workorders';
        }

        // Set active class on the appropriate nav item
        if (activeDataPage) {
            const activeLink = document.querySelector(`[data-page="${activeDataPage}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // Add click animation to nav links
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Add click feedback
                this.style.transform = 'scale(0.95) translateY(-1px)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);

                // Update active state immediately for better UX
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Update work orders badge if there's a count
        updateWorkOrdersBadge();
    });

    // Function to update work orders badge
    function updateWorkOrdersBadge(count = 0) {
        const badge = document.getElementById('workOrdersBadge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    // Function to show notification badge with animation
    function showNavNotification(page, count = 1) {
        const link = document.querySelector(`[data-page="${page}"]`);
        if (link) {
            let badge = link.querySelector('.nav-badge');
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'nav-badge';
                link.appendChild(badge);
            }

            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'flex';

            // Add bounce animation
            badge.style.animation = 'none';
            setTimeout(() => {
                badge.style.animation = 'pulse 2s infinite';
            }, 10);
        }
    }

    // Function to clear notification badge
    function clearNavNotification(page) {
        const link = document.querySelector(`[data-page="${page}"]`);
        if (link) {
            const badge = link.querySelector('.nav-badge');
            if (badge) {
                badge.style.display = 'none';
            }
        }
    }

    // Mobile Experience Optimizations

    // Detect device capabilities
    function detectDeviceCapabilities() {
        const capabilities = {
            isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
            isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            isHighDPI: window.devicePixelRatio > 1,
            supportsWebP: false,
            supportsPassiveEvents: false
        };

        // Test for WebP support
        const webP = new Image();
        webP.onload = webP.onerror = function () {
            capabilities.supportsWebP = (webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';

        // Test for passive event support
        try {
            const opts = Object.defineProperty({}, 'passive', {
                get: function() {
                    capabilities.supportsPassiveEvents = true;
                }
            });
            window.addEventListener('testPassive', null, opts);
            window.removeEventListener('testPassive', null, opts);
        } catch (e) {}

        return capabilities;
    }

    // Performance monitoring
    function monitorPerformance() {
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData) {
                        console.log('Page Load Performance:', {
                            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                            totalTime: perfData.loadEventEnd - perfData.fetchStart
                        });
                    }
                }, 0);
            });
        }
    }

    // Optimize touch interactions
    function optimizeTouchInteractions() {
        const capabilities = detectDeviceCapabilities();

        if (capabilities.isTouch) {
            // Add touch-specific classes
            document.body.classList.add('touch-device');

            // Optimize scroll performance
            const scrollElements = document.querySelectorAll('.mobile-tab-content, .work-order-card-body');
            scrollElements.forEach(element => {
                element.style.webkitOverflowScrolling = 'touch';
            });

            // Add touch feedback
            document.addEventListener('touchstart', function(e) {
                if (e.target.matches('.btn-modern, .mobile-tab, .work-order-action-btn, .nav-link')) {
                    e.target.style.transform = 'scale(0.98)';
                }
            }, capabilities.supportsPassiveEvents ? { passive: true } : false);

            document.addEventListener('touchend', function(e) {
                if (e.target.matches('.btn-modern, .mobile-tab, .work-order-action-btn, .nav-link')) {
                    setTimeout(() => {
                        e.target.style.transform = '';
                    }, 150);
                }
            }, capabilities.supportsPassiveEvents ? { passive: true } : false);
        }
    }

    // Viewport height fix for mobile browsers
    function fixViewportHeight() {
        const setVH = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };

        setVH();
        window.addEventListener('resize', setVH);
        window.addEventListener('orientationchange', () => {
            setTimeout(setVH, 100);
        });
    }

    // Lazy loading for images
    function setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Initialize mobile optimizations
    function initMobileOptimizations() {
        const capabilities = detectDeviceCapabilities();

        if (capabilities.isMobile) {
            document.body.classList.add('mobile-device');
            fixViewportHeight();
            optimizeTouchInteractions();
            setupLazyLoading();
            monitorPerformance();

            // Disable zoom on form inputs
            const metaViewport = document.querySelector('meta[name="viewport"]');
            if (metaViewport) {
                metaViewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no');
            }
        }
    }

    // Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileOptimizations);
    } else {
        initMobileOptimizations();
    }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
