/* Base styles for <PERSON><PERSON>gin - Mobile First Design */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #333333;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;

    /* Viewport height fix for mobile browsers */
    --vh: 1vh;
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #ecf0f1;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 70px; /* Space for mobile nav */
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: white;
    margin-right: 15px;
    opacity: 0.9;
}

/* Enhanced Mobile Bottom Navigation */
.mobile-nav {
    background: linear-gradient(135deg, var(--mobile-nav-bg) 0%, #1a252f 100%);
    backdrop-filter: blur(20px);
    box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
    height: 70px;
    z-index: 1020;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    padding: 0 8px;
}

.mobile-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    padding: 12px 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    border-radius: 16px;
    margin: 8px 2px;
    position: relative;
    overflow: hidden;
    min-height: 54px;
}

.mobile-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 0 0 3px 3px;
}

.mobile-nav .nav-link i {
    font-size: 1.4rem;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.mobile-nav .nav-link span {
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.mobile-nav .nav-link.active {
    color: white;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: translateY(-2px);
}

.mobile-nav .nav-link.active::before {
    width: 80%;
}

.mobile-nav .nav-link.active i {
    transform: scale(1.1);
    color: #667eea;
}

.mobile-nav .nav-link.active span {
    color: #667eea;
}

.mobile-nav .nav-link:hover:not(.active) {
    color: rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    transform: translateY(-1px);
}

.mobile-nav .nav-link:hover:not(.active)::before {
    width: 40%;
    opacity: 0.5;
}

.mobile-nav .nav-link:hover:not(.active) i {
    transform: scale(1.05);
}

/* Navigation Badge for Notifications */
.nav-badge {
    position: absolute;
    top: 8px;
    right: 12px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.6rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}

/* Main content area */
.app-content {
    flex: 1;
    padding: 1.75rem 0;
    margin-top: 0.5rem;
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border: none;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* Mobile-First Work Order Cards */
.work-order-card {
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
}

.work-order-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.work-order-card:hover {
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
    transform: translateY(-4px);
    border-color: rgba(102, 126, 234, 0.2);
}

.work-order-card:hover::before {
    opacity: 1;
}

.work-order-card-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.work-order-card-number {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.work-order-card-description {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
    margin-bottom: 8px;
}

.work-order-card-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.8rem;
    opacity: 0.8;
}

.work-order-card-body {
    padding: 16px;
}

.work-order-card-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.work-order-info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.work-order-info-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.work-order-info-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.work-order-card-actions {
    display: flex;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

.work-order-action-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    min-height: 44px; /* Minimum touch target */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.work-order-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.work-order-action-btn.primary {
    background: #007bff;
    color: white;
}

.work-order-action-btn.success {
    background: #28a745;
    color: white;
}

.work-order-action-btn.warning {
    background: #ffc107;
    color: #212529;
}

/* Status and Priority Badges */
.status-badge-mobile {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.priority-badge-mobile {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    color: white;
}

.priority-1-mobile { background: #dc3545; }
.priority-2-mobile { background: #fd7e14; }
.priority-3-mobile { background: #ffc107; color: #212529; }
.priority-4-mobile { background: #28a745; }
.priority-5-mobile { background: #6c757d; }

/* Mobile Tab System */
.mobile-tabs {
    display: flex;
    background: var(--card-bg);
    border-radius: 16px 16px 0 0;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
    border: 1px solid #e9ecef;
    border-bottom: none;
}

.mobile-tab {
    flex: 1;
    padding: 18px 12px;
    text-align: center;
    background: transparent;
    border: none;
    color: #6c757d;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 6px;
    position: relative;
    cursor: pointer;
}

.mobile-tab::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 3px 3px 0 0;
}

.mobile-tab.active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    transform: translateY(-2px);
}

.mobile-tab.active::before {
    width: 80%;
}

.mobile-tab:hover:not(.active) {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    color: #495057;
    transform: translateY(-1px);
}

.mobile-tab:hover:not(.active)::before {
    width: 40%;
    opacity: 0.5;
}

.mobile-tab i {
    font-size: 1.3rem;
    transition: all 0.3s ease;
}

.mobile-tab.active i {
    transform: scale(1.1);
    color: #667eea;
}

.mobile-tab-content {
    background: var(--card-bg);
    border-radius: 0 0 16px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-height: 200px;
    border: 1px solid #e9ecef;
    border-top: none;
    position: relative;
    overflow: hidden;
}

.mobile-tab-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
}

.mobile-tab-pane {
    display: none;
    padding: 24px;
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-tab-pane.active {
    display: block;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Task Cards */
.task-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.task-card-mobile:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-card-header-mobile {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.task-card-number-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.task-card-status-mobile {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-card-body-mobile {
    padding: 16px;
}

.task-card-description-mobile {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 12px;
    color: var(--text-color);
}

.task-card-meta-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    font-size: 0.8rem;
}

.task-meta-item-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.task-meta-label-mobile {
    color: #6c757d;
    font-weight: 600;
    font-size: 0.7rem;
    text-transform: uppercase;
}

.task-meta-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Material Cards */
.material-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    transition: all 0.2s ease;
}

.material-card-mobile:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.material-card-header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.material-item-number-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.material-quantity-mobile {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.material-description-mobile {
    font-size: 0.85rem;
    color: var(--text-color);
    line-height: 1.3;
    margin-bottom: 8px;
}

.material-details-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.75rem;
}

.material-detail-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.material-detail-label-mobile {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
}

.material-detail-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Task Sub-tabs */
.task-sub-tabs {
    margin-top: 16px;
}

.task-sub-tabs .mobile-tabs {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 0;
}

.task-sub-tabs .mobile-tab {
    font-size: 0.75rem;
    padding: 12px 8px;
    min-height: 40px;
}

.task-sub-tabs .mobile-tab i {
    font-size: 1rem;
}

.task-sub-tabs .mobile-tab-content {
    border-radius: 0 0 8px 8px;
    min-height: 120px;
}

.task-sub-tabs .mobile-tab-pane {
    padding: 16px;
}

/* Labor Cards */
.labor-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    transition: all 0.2s ease;
}

.labor-card-mobile:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.labor-card-header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.labor-person-name-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.labor-hours-mobile {
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.labor-details-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.75rem;
}

.labor-detail-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.labor-detail-label-mobile {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
}

.labor-detail-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Modern Button Styles */
.btn-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-modern:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

/* Modern Button Variants */
.btn-modern-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-modern-primary:hover {
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.btn-modern-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-modern-success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.btn-modern-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-modern-warning:hover {
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    color: #212529;
}

.btn-modern-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-modern-danger:hover {
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.btn-modern-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.btn-modern-outline:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Floating Action Button */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5);
    color: white;
}

.btn-fab:active {
    transform: scale(1.05);
}

/* Loading Button Animation */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Work Order Action Buttons */
.work-order-action-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    color: #495057;
    font-weight: 600;
    padding: 12px 16px;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.work-order-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.work-order-action-btn.primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.work-order-action-btn.primary:hover {
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    color: white;
}

.work-order-action-btn.success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border-color: #28a745;
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.work-order-action-btn.success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    color: white;
}

.work-order-action-btn.warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border-color: #ffc107;
    color: #212529;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.work-order-action-btn.warning:hover {
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    color: #212529;
}

/* Responsive adjustments for task cards */
@media (max-width: 576px) {
    .task-sub-tabs .mobile-tab {
        font-size: 0.7rem;
        padding: 10px 6px;
    }

    .task-sub-tabs .mobile-tab span {
        display: none;
    }

    .task-sub-tabs .mobile-tab i {
        font-size: 1.1rem;
    }

    .work-order-card-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .material-details-mobile,
    .labor-details-mobile {
        grid-template-columns: 1fr;
    }

    .btn-modern {
        padding: 10px 16px;
        font-size: 0.8rem;
    }

    .work-order-action-btn {
        padding: 10px 12px;
        font-size: 0.75rem;
    }
}

/* Mobile-First Responsive Design Enhancements */

/* Ensure minimum touch targets (44px) for accessibility */
.btn, .btn-modern, .btn-modern-primary, .btn-modern-success,
.btn-modern-warning, .btn-modern-danger, .btn-modern-outline,
.work-order-action-btn, .mobile-tab, .nav-link {
    min-height: 44px;
    min-width: 44px;
}

/* Enhanced focus states for accessibility */
.btn:focus, .btn-modern:focus, .mobile-tab:focus, .nav-link:focus,
.work-order-action-btn:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .work-order-card {
        border: 2px solid #000;
    }

    .mobile-tab.active {
        background: #000;
        color: #fff;
    }

    .btn-modern, .btn-modern-primary, .btn-modern-success,
    .btn-modern-warning, .btn-modern-danger {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .mobile-nav .nav-link::before,
    .mobile-tab::before,
    .work-order-card::before {
        transition: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #121212;
        --text-color: #e0e0e0;
        --border-color: #333;
        --card-bg: #1e1e1e;
        --header-bg: #1a1a1a;
        --footer-bg: #1a1a1a;
        --mobile-nav-bg: #1a1a1a;
        --shadow-color: rgba(0, 0, 0, 0.5);
    }

    .work-order-card {
        background: #1e1e1e;
        border-color: #333;
    }

    .mobile-tab-content {
        background: #1e1e1e;
        border-color: #333;
    }
}

/* Tablet breakpoint (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    .work-order-card {
        margin-bottom: 16px;
    }

    .work-order-card-info {
        grid-template-columns: repeat(3, 1fr);
    }

    .mobile-tabs {
        border-radius: 12px 12px 0 0;
    }

    .mobile-tab {
        padding: 16px 12px;
        min-height: 56px;
    }

    .mobile-tab span {
        display: block;
    }
}

/* Large mobile/small tablet (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
    .work-order-card-header {
        padding: 20px;
    }

    .work-order-card-body {
        padding: 20px;
    }

    .work-order-card-actions {
        flex-direction: row;
        gap: 12px;
    }

    .mobile-tab {
        padding: 14px 10px;
    }

    .btn-modern {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

/* Extra small devices (up to 479px) */
@media (max-width: 479px) {
    .work-order-card {
        margin-bottom: 12px;
        border-radius: 16px;
    }

    .work-order-card-header {
        padding: 16px;
    }

    .work-order-card-body {
        padding: 16px;
    }

    .work-order-card-actions {
        flex-direction: column;
        gap: 8px;
    }

    .work-order-action-btn {
        width: 100%;
        justify-content: center;
    }

    .mobile-tab span {
        font-size: 0.6rem;
    }

    .mobile-tab i {
        font-size: 1.2rem;
    }

    .btn-modern {
        width: 100%;
        padding: 14px 16px;
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
    .mobile-nav {
        height: 60px;
    }

    .mobile-nav .nav-link {
        padding: 8px 6px;
        min-height: 44px;
    }

    .mobile-nav .nav-link span {
        font-size: 0.6rem;
    }

    body {
        padding-bottom: 60px;
    }
}

/* Print styles */
@media print {
    .mobile-nav,
    .btn-fab,
    .work-order-card-actions {
        display: none !important;
    }

    .work-order-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        padding-bottom: 0;
    }
}

/* Enhanced Visual Feedback and Animations */

/* Skeleton Loading Animation */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-card {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.skeleton-header {
    height: 24px;
    border-radius: 12px;
    margin-bottom: 12px;
    width: 60%;
}

.skeleton-text {
    height: 16px;
    border-radius: 8px;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 40%;
}

.skeleton-text.medium {
    width: 70%;
}

.skeleton-text.long {
    width: 90%;
}

.skeleton-button {
    height: 44px;
    border-radius: 12px;
    width: 120px;
    margin-top: 16px;
}

/* Enhanced Button Animations */
.btn-pulse {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
}

/* Success Animation */
.success-animation {
    animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Error Shake Animation */
.error-shake {
    animation: error-shake 0.5s ease-in-out;
}

@keyframes error-shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Slide In Animations */
.slide-in-up {
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-down {
    animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-right {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fade Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Scale Animations */
.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-out {
    animation: scaleOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Loading Spinner Variations */
.spinner-modern {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-dots {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
}

.spinner-dots div {
    position: absolute;
    top: 16px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: spinner-dots 1.2s linear infinite;
}

.spinner-dots div:nth-child(1) {
    left: 4px;
    animation-delay: 0s;
}

.spinner-dots div:nth-child(2) {
    left: 16px;
    animation-delay: -0.4s;
}

.spinner-dots div:nth-child(3) {
    left: 28px;
    animation-delay: -0.8s;
}

@keyframes spinner-dots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Progress Bar */
.progress-modern {
    height: 6px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-modern-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-modern-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Toast Notifications */
.toast-modern {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    padding: 16px 20px;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid #667eea;
    max-width: 300px;
}

.toast-modern.show {
    transform: translateX(0);
}

.toast-modern.success {
    border-left-color: #28a745;
}

.toast-modern.error {
    border-left-color: #dc3545;
}

.toast-modern.warning {
    border-left-color: #ffc107;
}

/* Floating Elements */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Performance Optimizations */

/* GPU acceleration for smooth animations */
.work-order-card,
.mobile-tab,
.btn-modern,
.btn-modern-primary,
.btn-modern-success,
.btn-modern-warning,
.btn-modern-danger,
.btn-modern-outline,
.mobile-nav .nav-link {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Optimize scrolling performance */
.mobile-tab-content,
.work-order-card-body {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Reduce repaints during animations */
.work-order-card:hover,
.mobile-tab:hover,
.btn-modern:hover {
    contain: layout style paint;
}

/* Optimize font rendering */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Optimize image rendering */
img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Device-Specific Optimizations */

/* iPhone SE and similar small screens (320px) */
@media (max-width: 320px) {
    .work-order-card {
        margin-bottom: 8px;
        border-radius: 12px;
    }

    .work-order-card-header {
        padding: 12px;
    }

    .work-order-card-body {
        padding: 12px;
    }

    .mobile-tab {
        padding: 10px 4px;
        min-height: 40px;
    }

    .mobile-tab span {
        font-size: 0.55rem;
    }

    .mobile-tab i {
        font-size: 1rem;
    }

    .btn-modern {
        padding: 10px 12px;
        font-size: 0.75rem;
    }

    .mobile-nav {
        height: 65px;
    }

    .mobile-nav .nav-link {
        padding: 8px 4px;
    }

    .mobile-nav .nav-link span {
        font-size: 0.6rem;
    }
}

/* iPhone 12/13 Pro Max and similar large screens (428px) */
@media (min-width: 428px) and (max-width: 768px) {
    .work-order-card {
        margin-bottom: 24px;
    }

    .work-order-card-header {
        padding: 24px;
    }

    .work-order-card-body {
        padding: 24px;
    }

    .mobile-tab {
        padding: 20px 16px;
        min-height: 64px;
    }

    .mobile-tab i {
        font-size: 1.5rem;
    }

    .mobile-nav {
        height: 75px;
    }

    .mobile-nav .nav-link {
        padding: 14px 10px;
    }
}

/* iPad and tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    .work-order-card {
        max-width: 600px;
        margin: 0 auto 20px auto;
    }

    .mobile-tabs {
        max-width: 600px;
        margin: 0 auto;
    }

    .mobile-tab {
        padding: 18px 20px;
        min-height: 60px;
    }

    .btn-modern {
        padding: 14px 24px;
        font-size: 1rem;
    }
}

/* Touch-specific optimizations */
@media (pointer: coarse) {
    /* Increase touch targets for touch devices */
    .btn, .btn-modern, .mobile-tab, .nav-link {
        min-height: 48px;
        min-width: 48px;
    }

    /* Reduce hover effects on touch devices */
    .work-order-card:hover,
    .mobile-tab:hover,
    .btn-modern:hover {
        transform: none;
    }

    /* Optimize tap highlighting */
    * {
        -webkit-tap-highlight-color: rgba(102, 126, 234, 0.2);
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Allow text selection for content areas */
    .work-order-card-description,
    .task-card-description-mobile,
    .material-description-mobile,
    p, span, div[contenteditable] {
        -webkit-user-select: text;
        -khtml-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .work-order-card {
        border-width: 0.5px;
    }

    .mobile-tabs {
        border-width: 0.5px;
    }

    .mobile-tab-content {
        border-width: 0.5px;
    }
}

/* Accessibility improvements for screen readers */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus management for keyboard navigation */
.mobile-tab:focus,
.btn-modern:focus,
.work-order-action-btn:focus {
    outline: 3px solid rgba(102, 126, 234, 0.6);
    outline-offset: 2px;
    z-index: 10;
}

/* Ensure proper contrast ratios */
@media (prefers-contrast: high) {
    .work-order-card-description,
    .task-card-description-mobile,
    .material-description-mobile {
        color: #000;
        background: #fff;
    }

    .mobile-tab.active {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.input-group-text {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 50px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Helper classes for colors */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Responsive adjustments */
@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}
