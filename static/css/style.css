/* Base styles for <PERSON><PERSON>gin - Mobile First Design */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #333333;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #ecf0f1;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 70px; /* Space for mobile nav */
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: white;
    margin-right: 15px;
    opacity: 0.9;
}

/* Mobile bottom navigation */
.mobile-nav {
    background-color: var(--mobile-nav-bg);
    box-shadow: 0 -2px 10px var(--shadow-color);
    height: 60px;
    z-index: 1020;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 0;
    font-size: 0.75rem;
    transition: var(--transition);
    flex: 1;
    text-decoration: none;
}

.mobile-nav .nav-link i {
    font-size: 1.3rem;
    margin-bottom: 0.25rem;
}

.mobile-nav .nav-link.active {
    color: white;
}

.mobile-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Main content area */
.app-content {
    flex: 1;
    padding: 1.75rem 0;
    margin-top: 0.5rem;
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Card styles */
.card {
    background-color: var(--card-bg);
    border: none;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* Mobile-First Work Order Cards */
.work-order-card {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.work-order-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.work-order-card-header {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.work-order-card-number {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.work-order-card-description {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
    margin-bottom: 8px;
}

.work-order-card-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.8rem;
    opacity: 0.8;
}

.work-order-card-body {
    padding: 16px;
}

.work-order-card-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.work-order-info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.work-order-info-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.work-order-info-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.work-order-card-actions {
    display: flex;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

.work-order-action-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
    min-height: 44px; /* Minimum touch target */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.work-order-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.work-order-action-btn.primary {
    background: #007bff;
    color: white;
}

.work-order-action-btn.success {
    background: #28a745;
    color: white;
}

.work-order-action-btn.warning {
    background: #ffc107;
    color: #212529;
}

/* Status and Priority Badges */
.status-badge-mobile {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.priority-badge-mobile {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    color: white;
}

.priority-1-mobile { background: #dc3545; }
.priority-2-mobile { background: #fd7e14; }
.priority-3-mobile { background: #ffc107; color: #212529; }
.priority-4-mobile { background: #28a745; }
.priority-5-mobile { background: #6c757d; }

/* Mobile Tab System */
.mobile-tabs {
    display: flex;
    background: var(--card-bg);
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.mobile-tab {
    flex: 1;
    padding: 16px 12px;
    text-align: center;
    background: var(--card-bg);
    border: none;
    color: #6c757d;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    min-height: 44px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    position: relative;
}

.mobile-tab.active {
    background: #007bff;
    color: white;
}

.mobile-tab:hover:not(.active) {
    background: #f8f9fa;
    color: #495057;
}

.mobile-tab i {
    font-size: 1.1rem;
}

.mobile-tab-content {
    background: var(--card-bg);
    border-radius: 0 0 12px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 200px;
}

.mobile-tab-pane {
    display: none;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

.mobile-tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Task Cards */
.task-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.task-card-mobile:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-card-header-mobile {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.task-card-number-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.task-card-status-mobile {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.task-card-body-mobile {
    padding: 16px;
}

.task-card-description-mobile {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 12px;
    color: var(--text-color);
}

.task-card-meta-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    font-size: 0.8rem;
}

.task-meta-item-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.task-meta-label-mobile {
    color: #6c757d;
    font-weight: 600;
    font-size: 0.7rem;
    text-transform: uppercase;
}

.task-meta-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Material Cards */
.material-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    transition: all 0.2s ease;
}

.material-card-mobile:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.material-card-header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.material-item-number-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.material-quantity-mobile {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.material-description-mobile {
    font-size: 0.85rem;
    color: var(--text-color);
    line-height: 1.3;
    margin-bottom: 8px;
}

.material-details-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.75rem;
}

.material-detail-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.material-detail-label-mobile {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
}

.material-detail-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Task Sub-tabs */
.task-sub-tabs {
    margin-top: 16px;
}

.task-sub-tabs .mobile-tabs {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 0;
}

.task-sub-tabs .mobile-tab {
    font-size: 0.75rem;
    padding: 12px 8px;
    min-height: 40px;
}

.task-sub-tabs .mobile-tab i {
    font-size: 1rem;
}

.task-sub-tabs .mobile-tab-content {
    border-radius: 0 0 8px 8px;
    min-height: 120px;
}

.task-sub-tabs .mobile-tab-pane {
    padding: 16px;
}

/* Labor Cards */
.labor-card-mobile {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    transition: all 0.2s ease;
}

.labor-card-mobile:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.labor-card-header-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.labor-person-name-mobile {
    font-weight: 700;
    color: #007bff;
    font-size: 0.9rem;
}

.labor-hours-mobile {
    background: #e8f5e8;
    color: #2d5a2d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.labor-details-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.75rem;
}

.labor-detail-mobile {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.labor-detail-label-mobile {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
}

.labor-detail-value-mobile {
    color: var(--text-color);
    font-weight: 500;
}

/* Responsive adjustments for task cards */
@media (max-width: 576px) {
    .task-sub-tabs .mobile-tab {
        font-size: 0.7rem;
        padding: 10px 6px;
    }

    .task-sub-tabs .mobile-tab span {
        display: none;
    }

    .task-sub-tabs .mobile-tab i {
        font-size: 1.1rem;
    }

    .work-order-card-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .material-details-mobile,
    .labor-details-mobile {
        grid-template-columns: 1fr;
    }
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Form controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.input-group-text {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
}

/* Button styling */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 50px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Helper classes for colors */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Responsive adjustments */
@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}
